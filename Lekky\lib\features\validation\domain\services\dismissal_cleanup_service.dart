import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';

/// Service for cleaning up surplus dismissal entries during validation cycles
class DismissalCleanupService {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;

  /// Constructor
  DismissalCleanupService(
    this._meterReadingRepository,
    this._topUpRepository,
  );

  /// Clean up surplus dismissal entries during validation cycle
  Future<void> cleanupSurplusDismissalEntries() async {
    try {
      Logger.info(
          'DismissalCleanupService: Starting cleanup of surplus dismissal entries');

      // Get all entries
      final allReadings = await _meterReadingRepository.getAllMeterReadings();
      final allTopUps = await _topUpRepository.getAllTopUps();

      // Find dismissal entries that are no longer needed
      final dismissalEntriesToRemove = await _findSurplusDismissalEntries(
        allReadings,
        allTopUps,
      );

      if (dismissalEntriesToRemove.isNotEmpty) {
        Logger.info(
            'DismissalCleanupService: Found ${dismissalEntriesToRemove.length} surplus dismissal entries to remove');

        // Remove surplus dismissal entries
        for (final dismissalEntry in dismissalEntriesToRemove) {
          if (dismissalEntry.id != null) {
            await _meterReadingRepository
                .deleteMeterReading(dismissalEntry.id!);
            Logger.info(
                'DismissalCleanupService: Removed surplus dismissal entry ID ${dismissalEntry.id} from ${dismissalEntry.date}');
          }
        }

        Logger.info(
            'DismissalCleanupService: Successfully removed ${dismissalEntriesToRemove.length} surplus dismissal entries');
      } else {
        Logger.info(
            'DismissalCleanupService: No surplus dismissal entries found');
      }
    } catch (e) {
      Logger.error('DismissalCleanupService: Error during cleanup: $e');
    }
  }

  /// Find dismissal entries that are no longer needed
  Future<List<MeterReading>> _findSurplusDismissalEntries(
    List<MeterReading> allReadings,
    List<dynamic> allTopUps,
  ) async {
    final surplusEntries = <MeterReading>[];

    // Get only dismissal entries
    final dismissalEntries = allReadings
        .where((reading) => reading.status == EntryStatus.ignored)
        .toList();

    if (dismissalEntries.isEmpty) {
      return surplusEntries;
    }

    // Get all real entries (non-dismissal)
    final realEntries = <DateTime>[];
    for (final reading in allReadings) {
      if (reading.status != EntryStatus.ignored) {
        realEntries.add(reading.date);
      }
    }
    for (final topUp in allTopUps) {
      realEntries.add(topUp.date);
    }

    realEntries.sort();

    // Check each dismissal entry to see if it's still needed
    for (final dismissalEntry in dismissalEntries) {
      if (_isDismissalEntryNoLongerNeeded(dismissalEntry, realEntries)) {
        surplusEntries.add(dismissalEntry);
      }
    }

    return surplusEntries;
  }

  /// Check if a dismissal entry is no longer needed
  bool _isDismissalEntryNoLongerNeeded(
    MeterReading dismissalEntry,
    List<DateTime> realEntries,
  ) {
    // Find the gap that this dismissal entry was meant to fill
    DateTime? previousEntry;
    DateTime? nextEntry;

    for (int i = 0; i < realEntries.length; i++) {
      if (realEntries[i].isAfter(dismissalEntry.date)) {
        nextEntry = realEntries[i];
        if (i > 0) {
          previousEntry = realEntries[i - 1];
        }
        break;
      }
    }

    // If we can't find the gap context, keep the dismissal entry
    if (previousEntry == null || nextEntry == null) {
      return false;
    }

    // Check if the gap is now <= 62 days (no longer needs dismissal)
    final gap = nextEntry.difference(previousEntry).inDays;

    // Also verify this dismissal entry is at the expected position (previous + 62 days)
    final expectedDismissalDate = previousEntry.add(const Duration(days: 62));
    final isAtExpectedPosition =
        dismissalEntry.date.isAtSameMomentAs(expectedDismissalDate);

    return gap <= 62 || !isAtExpectedPosition;
  }
}
