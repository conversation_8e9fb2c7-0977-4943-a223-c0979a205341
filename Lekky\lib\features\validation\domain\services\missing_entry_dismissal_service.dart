import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../models/validation_issue.dart';

/// Service for dismissing missing entry validation issues
class MissingEntryDismissalService {
  final MeterReadingRepository _meterReadingRepository;

  /// Constructor
  MissingEntryDismissalService(this._meterReadingRepository);

  /// Dismiss a missing entry issue by creating a single dismissal entry
  Future<List<MeterReading>> dismissMissingEntryIssue(
      ValidationIssue issue) async {
    try {
      final startDate = DateTime.parse(issue.metadata!['start_date']);
      final endDate = DateTime.parse(issue.metadata!['end_date']);
      final gapDays = issue.metadata!['gap_days'] as int;

      Logger.info(
          'MissingEntryDismissalService: Dismissing gap from $startDate to $endDate ($gapDays days)');

      // Create single dismissal entry 62 days into the gap
      final dismissalDate = startDate.add(const Duration(days: 62));

      // Ensure dismissal date is before end date
      if (dismissalDate.isAfter(endDate) ||
          dismissalDate.isAtSameMomentAs(endDate)) {
        Logger.error(
            'MissingEntryDismissalService: Cannot create dismissal entry - gap too short');
        throw Exception('Gap too short for dismissal entry');
      }

      final dismissalEntry = MeterReading(
        value: 0.0, // Zero value for dismissal entries
        date: dismissalDate,
        status: EntryStatus.ignored,
        notes:
            'Dismissed missing entry gap: $gapDays days (${_formatDate(startDate)} - ${_formatDate(endDate)})',
      );

      // Save the dismissal entry
      final id = await _meterReadingRepository.addMeterReading(dismissalEntry);
      Logger.info(
          'MissingEntryDismissalService: Saved dismissal entry with ID $id for date $dismissalDate');

      Logger.info(
          'MissingEntryDismissalService: Successfully created dismissal entry');

      return [dismissalEntry];
    } catch (e) {
      Logger.error(
          'MissingEntryDismissalService: Error dismissing missing entry issue: $e');
      rethrow;
    }
  }

  /// Check if a gap period has been dismissed
  bool isGapDismissed(
      List<MeterReading> readings, DateTime start, DateTime end) {
    // Look for dismissal entry at start + 62 days
    final expectedDismissalDate = start.add(const Duration(days: 62));

    // Check if dismissal date would be within the gap
    if (expectedDismissalDate.isAfter(end) ||
        expectedDismissalDate.isAtSameMomentAs(end)) {
      return false;
    }

    return readings.any((reading) =>
        reading.status == EntryStatus.ignored &&
        reading.date.isAtSameMomentAs(expectedDismissalDate));
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
