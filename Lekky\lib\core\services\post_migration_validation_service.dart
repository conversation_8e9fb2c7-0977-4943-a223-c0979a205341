import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../utils/logger.dart';
import '../../features/validation/domain/services/data_integrity_service.dart';
import '../di/service_locator.dart';

/// Service to handle post-migration validation tasks
class PostMigrationValidationService {
  static const String _validationFlagKey =
      'needs_validation_after_v7_migration';

  /// Check if validation is needed after migration and run it
  static Future<void> checkAndRunPostMigrationValidation() async {
    try {
      final databaseHelper = serviceLocator<DatabaseHelper>();
      final db = await databaseHelper.database;

      Logger.info(
          'PostMigrationValidationService: Running validation check...');

      // Check if app_metadata table exists
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='app_metadata'",
      );

      if (tables.isEmpty) {
        Logger.info(
            'PostMigrationValidationService: app_metadata table does not exist, skipping validation flag check');
        return;
      }

      // Check if validation flag exists
      final result = await db.query(
        'app_metadata',
        where: 'key = ?',
        whereArgs: [_validationFlagKey],
      );

      if (result.isNotEmpty) {
        Logger.info(
            'PostMigrationValidationService: Found validation flag, running post-migration validation');

        // Run validation on all entries
        final dataIntegrityService = serviceLocator<DataIntegrityService>();
        await dataIntegrityService.validateAndUpdateAllMeterReadings();
        await dataIntegrityService.validateAndUpdateAllTopUps();

        // Remove the flag after successful validation
        await db.delete(
          'app_metadata',
          where: 'key = ?',
          whereArgs: [_validationFlagKey],
        );

        Logger.info(
            'PostMigrationValidationService: Post-migration validation completed and flag removed');
      } else {
        Logger.info(
            'PostMigrationValidationService: No validation flag found, skipping post-migration validation');
      }
    } catch (e) {
      Logger.error(
          'PostMigrationValidationService: Failed to run post-migration validation: $e');
      // Don't rethrow - this shouldn't break the app
    }
  }
}
