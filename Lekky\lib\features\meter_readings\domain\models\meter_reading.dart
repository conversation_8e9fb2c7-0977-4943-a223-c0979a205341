import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/utils/logger.dart';

/// Model class for meter readings
class MeterReading {
  /// Unique identifier
  final int? id;

  /// Meter reading value in currency
  final double value;

  /// Date of the reading
  final DateTime date;

  /// Entry status (valid, invalid)
  final EntryStatus status;

  /// Optional notes
  final String? notes;

  /// Whether the reading is estimated
  final bool isEstimated;

  /// Creation date
  final DateTime createdAt;

  /// Last update date
  final DateTime updatedAt;

  /// Recent average usage per day for the period ending with this reading
  final double? recentAveragePerDay;

  /// Constructor
  MeterReading({
    this.id,
    required this.value,
    required this.date,
    this.status = EntryStatus.valid,
    this.notes,
    this.recentAveragePerDay,
    this.isEstimated = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Convenience getter for backward compatibility
  bool get isValid => status == EntryStatus.valid;

  /// Check if entry is invalid
  bool get isInvalid => status == EntryStatus.invalid;

  /// Check if entry is ignored/dismissed
  bool get isIgnored => status == EntryStatus.ignored;

  /// Create a copy of this meter reading with optional new values
  MeterReading copyWith({
    int? id,
    double? value,
    DateTime? date,
    EntryStatus? status,
    bool? isValid,
    String? notes,
    double? recentAveragePerDay,
    bool? isEstimated,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MeterReading(
      id: id ?? this.id,
      value: value ?? this.value,
      date: date ?? this.date,
      status: status ??
          (isValid != null
              ? (isValid ? EntryStatus.valid : EntryStatus.invalid)
              : this.status),
      notes: notes ?? this.notes,
      recentAveragePerDay: recentAveragePerDay ?? this.recentAveragePerDay,
      isEstimated: isEstimated ?? this.isEstimated,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Convert to a map for database operations
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'value': value,
      'date': date.toIso8601String(),
      // Use both status and is_valid fields for compatibility
      'status': status.value,
      'is_valid': isValid ? 1 : 0,
      'notes': notes,
      'recent_average_per_day': recentAveragePerDay,
      'is_estimated': isEstimated ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a meter reading from a map
  factory MeterReading.fromMap(Map<String, dynamic> map) {
    // DEBUG: Log raw database map
    Logger.info('MeterReading.fromMap() - Raw map: $map');

    // Prioritize status field for dismissal entries, fallback to is_valid
    EntryStatus status;
    if (map.containsKey('status')) {
      final statusValue = map['status'] as int;
      status = EntryStatus.fromValue(statusValue);
      Logger.info(
          'MeterReading.fromMap() - Using status field: $statusValue -> $status');
    } else if (map.containsKey('is_valid')) {
      final isValidValue = map['is_valid'] as int;
      status = isValidValue == 1 ? EntryStatus.valid : EntryStatus.invalid;
      Logger.info(
          'MeterReading.fromMap() - Using is_valid field: $isValidValue -> $status');
    } else {
      // Default to valid if neither field exists
      status = EntryStatus.valid;
      Logger.info(
          'MeterReading.fromMap() - No status/is_valid field, defaulting to: $status');
    }

    return MeterReading(
      id: map['id'] as int?,
      value: map['value'] as double,
      date: DateTime.parse(map['date'] as String),
      status: status,
      notes: map['notes'] as String?,
      recentAveragePerDay: map['recent_average_per_day'] as double?,
      isEstimated: map['is_estimated'] != null
          ? (map['is_estimated'] as int) == 1
          : false,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  @override
  String toString() {
    return 'MeterReading(id: $id, value: $value, date: $date, status: $status, notes: $notes, isEstimated: $isEstimated)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MeterReading &&
        other.id == id &&
        other.value == value &&
        other.date.isAtSameMomentAs(date) &&
        other.status == status &&
        other.notes == notes &&
        other.isEstimated == isEstimated;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        value.hashCode ^
        date.hashCode ^
        status.hashCode ^
        notes.hashCode ^
        isEstimated.hashCode;
  }
}
